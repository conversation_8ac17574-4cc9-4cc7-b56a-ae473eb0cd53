
<sql splitStatements="false">
    <![CDATA[
                CREATE OR REPLACE FUNCTION sandf.fn_get_renewal_charges_and_targeted_claims_global(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    employee_class_count INTEGER;
    employee_classes TEXT[];
    has_rtq_only BOOLEAN := FALSE;
    result JSONB;
BEGIN
    -- Step 1: Detect employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    -- Log the detected employee classes for debugging
    RAISE NOTICE 'Global function detected % employee classes: %', employee_class_count, employee_classes;

    -- Step 2: Check if we have only RTQ employee class
    IF employee_class_count = 1 AND employee_classes[1] = 'RTQ' THEN
        has_rtq_only := TRUE;
    END IF;

    -- Step 3: Route to appropriate function based on employee class data
    IF has_rtq_only THEN
        -- Single RTQ employee class - use original plan-design function
        RAISE NOTICE 'Routing to single-class plan design function (RTQ only)';

        -- Call the original plan design function
        SELECT sandf.fn_get_renewal_charges_and_targeted_claims_RTQ(
            plan_uuid_param,
            user_id_param
        ) INTO result;

    ELSE
        -- Multiple employee classes or non-RTQ - use multi-class function
        RAISE NOTICE 'Routing to multi-class plan design function (% classes)', employee_class_count;

        -- Call the multi-class plan design function
        SELECT sandf.fn_get_renewal_charges_and_targeted_claims_multi_class(
            plan_uuid_param,
            user_id_param
        ) INTO result;

    END IF;

    -- Step 4: Return the result from the appropriate function
    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Handle any errors and provide meaningful error information
        RAISE EXCEPTION 'Error in global plan design function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;

END;
$$;
]]>
        </sql>
