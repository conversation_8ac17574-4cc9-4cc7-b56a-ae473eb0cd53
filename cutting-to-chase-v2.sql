        <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION fn_get_cutting_chase_multi_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
            benefit_premiums_key TEXT := 'benefitPremiums';
            quote_record RECORD;
            benefit_premiums JSONB;
            carriers_array JSONB := '[]'::jsonb;
            sections_array JSONB := '[]'::jsonb;
            section_obj JSONB;
            benefits_array JSONB := '[]'::jsonb;
            benefit_obj JSONB;
            carrier_name TEXT;
            benefit_map JSONB := '{}'::jsonb;

            -- CuttingToTheChase specific variables for calculations
            total_monthly_premium NUMERIC;
            annual_premium NUMERIC;
            first_carrier_annual NUMERIC := NULL;
            difference_amount NUMERIC;
            difference_percentage NUMERIC;
            carrier_order INT := 0;

            -- Manual calculation variables (like rate-sheet-v2.sql)
            key_name TEXT;
            val JSONB;
            mapped_key_name TEXT;
            should_include_key BOOLEAN;
            coverage_order TEXT[] := ARRAY['single', 'couple', 'family'];
            coverage_type TEXT;
            subval JSONB;
            premium_value NUMERIC;

            -- Config variables for manual calculation
            config_json JSONB;
            benefit_to_premium_map JSONB;

            -- New rankings array to store rank as strings
            rankings_array JSONB := '[]'::jsonb;

            -- Multi-class support variables
            employee_classes TEXT[];
            employee_class_count INTEGER;
            current_employee_class TEXT;

            -- Carrier totals across all classes
            carrier_totals_map JSONB := '{}'::jsonb;
            carrier_order_map JSONB := '{}'::jsonb;

            BEGIN
            -- Get configuration from config table (for manual calculation)
            SELECT json_data INTO config_json
            FROM config.json_storage
            WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
            LIMIT 1;

            -- Extract configuration mappings
            IF config_json IS NOT NULL THEN
                benefit_to_premium_map := config_json -> 'benefitToPremium';
            ELSE
                benefit_to_premium_map := '{}'::jsonb;
            END IF;

            RAISE NOTICE 'Configuration loaded for manual calculation:';
            RAISE NOTICE 'benefit_to_premium_map: %', benefit_to_premium_map;

            -- Step 1: Detect employee classes for this plan
            SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
            INTO employee_class_count, employee_classes
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb;

            -- Step 2: Process all employee classes to sum premiums per carrier
            FOREACH current_employee_class IN ARRAY employee_classes
            LOOP
                -- Get all quotes for this employee class and calculate totals (ordered by user preference)
                FOR quote_record IN
                SELECT ecq.formatted_quote_details,
                       c.description AS carrier_description,
                       q.quote_id,
                       q.quote_uuid
                FROM sandf.plan p
                         JOIN sandf.quote q ON q.plan_id = p.plan_id
                         JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
                         JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
                         JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
                WHERE p.plan_uuid = plan_uuid_param::uuid
                AND ec.name = current_employee_class
                AND ecq.formatted_quote_details IS NOT NULL
                AND ecq.formatted_quote_details != '{}'::jsonb
                ORDER BY sandf.get_user_preference_order(
                    user_id_param,
                    plan_uuid_param,
                    q.quote_id,
                    q.quote_uuid,
                    999999
                ) ASC, c.description ASC
                    LOOP
                    carrier_name := quote_record.carrier_description;

                    -- Track carrier order for first occurrence
                    IF NOT carrier_order_map ? carrier_name THEN
                        carrier_order := carrier_order + 1;
                        carrier_order_map := carrier_order_map || jsonb_build_object(
                            carrier_name, jsonb_build_object('order', carrier_order)
                        );
                    END IF;

                    -- Add carrier to carriers array if not already present
                    IF NOT carriers_array ? carrier_name THEN
                        carriers_array := carriers_array || jsonb_build_array(carrier_name);
                    END IF;

                    -- Extract benefit premiums from formatted_quote_details
                    benefit_premiums := quote_record.formatted_quote_details -> benefit_premiums_key;

                    -- Process benefit premiums if found
                    IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN
                        -- Manual calculation like rate-sheet-v2.sql (sum all individual premiums)
                        total_monthly_premium := 0;

                        RAISE NOTICE 'Starting manual calculation for carrier % class %', carrier_name, current_employee_class;

                        -- Sum all premiums for this carrier in this class (excluding pre-calculated totals)
                        FOR key_name, val IN
                            SELECT j.k1, j.v1
                            FROM jsonb_each(benefit_premiums) AS j(k1, v1)
                        LOOP
                            RAISE NOTICE 'Processing benefit key: % with value: %', key_name, val::TEXT;
                            IF key_name NOT IN ('totalMonthlyPremiums', 'annualPremium') THEN
                                -- Get mapped key name from config
                                IF benefit_to_premium_map ? key_name THEN
                                    mapped_key_name := benefit_to_premium_map ->> key_name;
                                ELSE
                                    mapped_key_name := key_name;
                                END IF;

                                RAISE NOTICE 'Key: % mapped to: %', key_name, mapped_key_name;

                                -- Include all keys for cutting chase (no filtering like rate-sheet)
                                should_include_key := TRUE;

                                RAISE NOTICE 'Should include key %: %', mapped_key_name, should_include_key;

                                IF should_include_key THEN
                                    -- Check if nested structure (single/couple/family)
                                    IF val ? 'single' OR val ? 'couple' OR val ? 'family' THEN
                                        RAISE NOTICE 'Processing nested structure for key: %', mapped_key_name;
                                        FOREACH coverage_type IN ARRAY coverage_order
                                        LOOP
                                            IF val ? coverage_type THEN
                                                subval := val -> coverage_type;
                                                RAISE NOTICE 'Coverage Type: %, Subval: %', coverage_type, subval::TEXT;
                                                IF subval ? 'premium' THEN
                                                    RAISE NOTICE 'Raw premium value from data: %', subval ->> 'premium';
                                                    premium_value := sandf.safe_parse_numeric(subval ->> 'premium');
                                                    RAISE NOTICE 'Parsed Premium Value for %: %', coverage_type, premium_value;
                                                    total_monthly_premium := total_monthly_premium + premium_value;
                                                    RAISE NOTICE 'Running Total - Monthly: %', total_monthly_premium;
                                                END IF;
                                            END IF;
                                        END LOOP;
                                    ELSE
                                        -- Direct premium value
                                        RAISE NOTICE 'Processing direct premium value for key: %', mapped_key_name;
                                        IF val ? 'premium' THEN
                                            RAISE NOTICE 'Raw premium value from data (direct): %', val ->> 'premium';
                                            premium_value := sandf.safe_parse_numeric(val ->> 'premium');
                                            RAISE NOTICE 'Raw Premium: %, Parsed Premium Value: %', val ->> 'premium', premium_value;
                                            total_monthly_premium := total_monthly_premium + premium_value;
                                            RAISE NOTICE 'Running Total - Monthly: %', total_monthly_premium;
                                        END IF;
                                    END IF;
                                END IF;
                            END IF;
                        END LOOP;

                        RAISE NOTICE 'Finished manual calculation for carrier % class %: total = %',
                            carrier_name, current_employee_class, total_monthly_premium;

                        -- Add to carrier totals (sum across all employee classes)
                        IF carrier_totals_map ? carrier_name THEN
                            -- Add to existing total
                            DECLARE
                                previous_total NUMERIC := (carrier_totals_map ->> carrier_name)::NUMERIC;
                                new_total NUMERIC := previous_total + total_monthly_premium;
                            BEGIN
                                carrier_totals_map := jsonb_set(
                                    carrier_totals_map,
                                    ARRAY[carrier_name],
                                    to_jsonb(new_total)
                                );
                                RAISE NOTICE 'Updated carrier % total: % + % = %',
                                    carrier_name, previous_total, total_monthly_premium, new_total;
                            END;
                        ELSE
                            -- Initialize total for this carrier
                            carrier_totals_map := carrier_totals_map || jsonb_build_object(
                                carrier_name, total_monthly_premium
                            );
                            RAISE NOTICE 'Initialized carrier % total: %', carrier_name, total_monthly_premium;
                        END IF;
                    ELSE
                        RAISE NOTICE 'No benefit premiums found for carrier % class %', carrier_name, current_employee_class;
                    END IF;
                END LOOP;
            END LOOP;

            -- Log carrier totals summary
            RAISE NOTICE '=== CUTTING CHASE CARRIER TOTALS SUMMARY ===';
            FOR carrier_name IN SELECT jsonb_object_keys(carrier_totals_map)
            LOOP
                total_monthly_premium := (carrier_totals_map ->> carrier_name)::NUMERIC;
                RAISE NOTICE 'CARRIER: %', carrier_name;
                RAISE NOTICE '  Total Monthly Premium (All Classes): $%', COALESCE(TO_CHAR(total_monthly_premium, 'FM999,999,999.00'), '0.00');
                RAISE NOTICE '  Annual Premium (Monthly x 12): $%', COALESCE(TO_CHAR(total_monthly_premium * 12, 'FM999,999,999.00'), '0.00');
                RAISE NOTICE '---';
            END LOOP;
            RAISE NOTICE '=== END OF CARRIER TOTALS ===';
            RAISE NOTICE '';

            -- Step 3: Build rankings array based on carrier order
            FOR carrier_name IN
                SELECT jsonb_object_keys(carrier_order_map)
                ORDER BY (carrier_order_map -> jsonb_object_keys(carrier_order_map) ->> 'order')::INTEGER
            LOOP
                rankings_array := rankings_array || to_jsonb(
                    (carrier_order_map -> carrier_name ->> 'order')::TEXT
                );
            END LOOP;

            -- Step 4: Calculate differences and build benefit map using summed totals
            carrier_order := 0;
            first_carrier_annual := NULL;

            FOR carrier_name IN
                SELECT jsonb_object_keys(carrier_totals_map)
                ORDER BY (carrier_order_map -> jsonb_object_keys(carrier_totals_map) ->> 'order')::INTEGER
            LOOP
                carrier_order := carrier_order + 1;
                total_monthly_premium := (carrier_totals_map ->> carrier_name)::NUMERIC;
                annual_premium := total_monthly_premium * 12;

                -- Set first carrier annual for difference calculations
                IF first_carrier_annual IS NULL THEN
                    first_carrier_annual := annual_premium;
                END IF;

                -- Calculate differences
                difference_amount := annual_premium - first_carrier_annual;
                IF first_carrier_annual != 0 THEN
                    difference_percentage := (difference_amount / first_carrier_annual) * 100;
                ELSE
                    difference_percentage := 0;
                END IF;

                -- Add totalMonthlyPremiums benefit (always include for CuttingToTheChase)
                IF NOT benefit_map ? 'calculations.totalMonthlyPremiums' THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        'calculations.totalMonthlyPremiums',
                        jsonb_build_object(
                            'name', 'Total Monthly Premiums',
                            'key', 'totalMonthlyPremiums',
                            'section', 'calculations',
                            'values', '{}'::jsonb
                        )
                    );
                END IF;
                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY['calculations.totalMonthlyPremiums', 'values', carrier_name],
                    to_jsonb(sandf.fn_format_currency_with_symbol_java_style(total_monthly_premium))
                );

                -- Add differencePercentage benefit (always include for CuttingToTheChase)
                IF carrier_order > 1 THEN
                    IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                        benefit_map := benefit_map || jsonb_build_object(
                            'calculations.differencePercentage',
                            jsonb_build_object(
                                'name', 'Percentage Different From #1',
                                'key', 'differencePercentage',
                                'section', 'calculations',
                                'values', '{}'::jsonb
                            )
                        );
                    END IF;
                    benefit_map := jsonb_set(
                        benefit_map,
                        ARRAY['calculations.differencePercentage', 'values', carrier_name],
                        to_jsonb(sandf.fn_format_percentage_java_style(difference_percentage))
                    );
                ELSE
                    -- For first carrier, set empty value "-" for difference field
                    IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                        benefit_map := benefit_map || jsonb_build_object(
                            'calculations.differencePercentage',
                            jsonb_build_object(
                                'name', 'Percentage Different From #1',
                                'key', 'differencePercentage',
                                'section', 'calculations',
                                'values', '{}'::jsonb
                            )
                        );
                    END IF;
                    benefit_map := jsonb_set(
                        benefit_map,
                        ARRAY['calculations.differencePercentage', 'values', carrier_name],
                        to_jsonb('-'::text)
                    );
                END IF;
            END LOOP;

            -- Step 5: Build benefits array from benefit_map (only totalMonthlyPremiums and differencePercentage)
            IF benefit_map ? 'calculations.totalMonthlyPremiums' THEN
                benefit_obj := benefit_map -> 'calculations.totalMonthlyPremiums';
                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;

            IF benefit_map ? 'calculations.differencePercentage' THEN
                benefit_obj := benefit_map -> 'calculations.differencePercentage';
                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;

            -- Only create the "Totals" section if we have benefits
            IF jsonb_array_length(benefits_array) > 0 THEN
                section_obj := jsonb_build_object(
                    'name', 'Totals',
                    'id', 'calculations',
                    'benefits', benefits_array
                );
                sections_array := sections_array || jsonb_build_array(section_obj);
            END IF;

            -- Return the results in the new format including rankings
            RETURN jsonb_build_object(
                        'carriers', carriers_array,
                        'sections', sections_array,
                        'rankings', rankings_array
                       )::text;
END;

$$;
]]>
        </sql>