
<sql splitStatements="false">
    <![CDATA[
                CREATE OR REPLACE FUNCTION sandf.fn_get_deviations_global(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    max_deviations_per_carrier INTEGER DEFAULT 4
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    employee_class_count INTEGER;
    employee_classes TEXT[];
    has_rtq_only BOOLEAN := FALSE;
    result JSONB;
BEGIN
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;


    IF employee_class_count = 1 AND employee_classes[1] = 'RTQ' THEN
        has_rtq_only := TRUE;
    END IF;

    IF has_rtq_only THEN
        SELECT sandf.fn_get_deviations_RTQ(
            plan_uuid_param,
            user_id_param,
            max_deviations_per_carrier
        ) INTO result;

    ELSE
        SELECT sandf.fn_get_deviations_multi_class(
            plan_uuid_param,
            user_id_param,
            max_deviations_per_carrier
        ) INTO result;

    END IF;

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in global plan design function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;

END;
$$;
]]>
        </sql>
