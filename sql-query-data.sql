<changeSet author="Ajay" id="************-15">
        <sql splitStatements="false"><![CDATA[
            WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
                RETURNING audit_log_revision_id
                ),
                upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'otherConsiderations',
                'SELECT
                    data -> ''RatingFactorsAndGuarantees'' AS "report_data",
                    true AS "needsPreprocessing",
                    ''OtherConsidernationsFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_other_consideration_global(:planUuid, :userUuid
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
                )
            ON CONFLICT (name)
                DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
                RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
                )
            INSERT INTO config.audit_log_sql_storage (
                sql_storage_id,
                sql_storage_uuid,
                revision_id,
                revision_type,
                name,
                sql_query,
                last_modified_by,
                last_modified_date
            )
            SELECT
                u.sql_storage_id,
                u.sql_storage_uuid,
                ir.audit_log_revision_id,
                1,
                u.name,
                u.sql_query,
                u.last_modified_by,
                u.last_modified_date
            FROM upserts u, inserted_revision ir;
            ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'ratesPerEE',
                'SELECT
                    data AS "report_data",
                    true AS "needsPreprocessing",
                    ''RatesPerEmployeeFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_rate_per_employee_global(
                        :planUuid,
                        :userUuid,
                        :numberOfEmployees,
                        :includesArray::text[],
                        :excludesArray::text[]
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'targetedClaims',
                'SELECT
                    (data -> ''targetedClaims'') AS "report_data",
                    true AS "needsPreprocessing",
                    ''TargetedClaimsFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_renewal_charges_and_targeted_claims_global(
                        :planUuid, :userUuid
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'renewalCharges',
                'SELECT
                    (data -> ''renewalCharges'') AS "report_data",
                    true AS "needsPreprocessing",
                    ''RenewalChargesFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_renewal_charges_and_targeted_claims_global(
                        :planUuid,
                        :userUuid
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'planDesign',
                'SELECT
                    data AS "report_data",
                    true AS "needsPreprocessing",
                    ''PlanDesignFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_plan_design_global(
                        :planUuid,
                        :userUuid,
                        :includesArray::text[],
                        :excludesArray::text[]
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'cuttingChase',
                'SELECT
                    data AS "report_data",
                    true AS "needsPreprocessing",
                    ''CuttingToTheChaseFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_cutting_to_the_chase_global(
                        :planUuid,
                        :userUuid
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'rateSheet1',
                'SELECT
                    data AS "report_data",
                    true AS "needsPreprocessing",
                    ''RateSheetFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_rate_sheet_global(
                        :planUuid,
                        :userUuid,
                        :includesArray::text[],
                        :excludesArray::text[]
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
       
        <sql splitStatements="false"><![CDATA[
        WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
            RETURNING audit_log_revision_id
        ),
        upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'planOverview',
                'SELECT
                    data AS "report_data",
                    true AS "needsPreprocessing",
                    ''planOverviewFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_plan_overview(
                        :includesArray::text[]
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
            )
            ON CONFLICT (name)
            DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
            RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
        )
        INSERT INTO config.audit_log_sql_storage (
            sql_storage_id,
            sql_storage_uuid,
            revision_id,
            revision_type,
            name,
            sql_query,
            last_modified_by,
            last_modified_date
        )
        SELECT
            u.sql_storage_id,
            u.sql_storage_uuid,
            ir.audit_log_revision_id,
            1,
            u.name,
            u.sql_query,
            u.last_modified_by,
            u.last_modified_date
        FROM upserts u, inserted_revision ir;
    ]]></sql>
     <sql splitStatements="false"><![CDATA[
            WITH inserted_revision AS (
            INSERT INTO config.audit_log_revision (auditor, "timestamp")
            VALUES ('system', CAST(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) AS bigint))
                RETURNING audit_log_revision_id
                ),
                upserts AS (
            INSERT INTO config.sql_storage (
                sql_storage_uuid,
                name,
                sql_query,
                created_by,
                last_modified_by,
                created_date,
                last_modified_date
            )
            VALUES (
                gen_random_uuid(),
                'deviations',
                'SELECT
                    data  AS "report_data",
                    true AS "needsPreprocessing",
                    ''DeviationsFTL'' AS "templateNameKey"
                FROM (
                    SELECT sandf.fn_get_deviations_global(:planUuid, :userUuid,4
                    ) AS data
                ) sub;',
                'system',
                'system',
                now(),
                now()
                )
            ON CONFLICT (name)
                DO UPDATE SET
                sql_query = EXCLUDED.sql_query,
                last_modified_by = 'system',
                last_modified_date = now()
                RETURNING sql_storage_id, sql_storage_uuid, name, sql_query, last_modified_by, last_modified_date
                )
            INSERT INTO config.audit_log_sql_storage (
                sql_storage_id,
                sql_storage_uuid,
                revision_id,
                revision_type,
                name,
                sql_query,
                last_modified_by,
                last_modified_date
            )
            SELECT
                u.sql_storage_id,
                u.sql_storage_uuid,
                ir.audit_log_revision_id,
                1,
                u.name,
                u.sql_query,
                u.last_modified_by,
                u.last_modified_date
            FROM upserts u, inserted_revision ir;
            ]]></sql>
    </changeSet>