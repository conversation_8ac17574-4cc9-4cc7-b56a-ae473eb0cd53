  <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION fn_get_plan_design_report_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    plan_details_key TEXT := 'planDetails';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB;
    benefit_obj JSONB;
    carrier_name TEXT;
    group_name TEXT;
    group_display_name TEXT;
    benefit_name TEXT;
    benefit_key TEXT;
    benefit_values JSONB := '{}'::jsonb;
    field_details JSONB;
    field_detail JSONB;
    carrier_value TEXT;
    section_map JSONB := '{}'::jsonb;
    section_names TEXT[];
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefit_map JSONB := '{}'::jsonb;
    benefit_keys TEXT[];
    benefit_key_item TEXT;
    section_order_record RECORD;
    benefit_order_record RECORD;

    -- Variables for skip condition logic
    skip_fields TEXT[] := ARRAY['maximumLife', 'maximumADAD'];
    coverage_life_values JSONB := '{}'::jsonb;
    field_values_to_check JSONB := '{}'::jsonb;
    should_skip_field BOOLEAN;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;

    -- Variables for carrier ordering
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    quote_uuid_val UUID;

    -- Pagination variables
    MAX_BENEFITS_PER_PAGE INTEGER := 10;
    all_benefits JSONB := '[]'::jsonb;
    all_sections JSONB := '[]'::jsonb;
    current_page_benefits INTEGER := 0;
    current_page_sections JSONB := '[]'::jsonb;
    current_section_benefits JSONB := '[]'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    total_benefits INTEGER;
    current_section_name TEXT;
    current_section_id TEXT;
    current_section_display_name TEXT;
    section_idx INTEGER;
    benefit_idx INTEGER;

BEGIN
    -- First pass: Build carrier order map and collect all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Use the new function to get carrier order
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999  -- default order
        );

        RAISE NOTICE 'Carrier: %, Quote ID: %, Order: %',
                     carrier_name, quote_record.quote_id, carrier_order;

        -- Build carrier order map using carrier name as key
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    -- Second pass: Process quotes to build plan data with proper ordering
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = 'RTQ'
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        -- Order using the new function
        ORDER BY
            sandf.get_user_preference_order(
                user_id_param,
                plan_uuid_param,
                q.quote_id,
                q.quote_uuid,
                999999
            ) ASC,
            c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Continue with existing logic for processing plan details
        plan_details := quote_record.formatted_quote_details -> plan_details_key;

        IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
            fields := plan_details -> 'fields';
            IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                FOR field_item IN SELECT jsonb_array_elements(fields)
                LOOP
                    group_name := field_item ->> 'groupName';
                    IF group_name IS NULL THEN
                        group_name := field_item ->> 'name';
                    END IF;

                    SELECT friendly INTO group_display_name
                    FROM sandf.ui_field
                    WHERE name = group_name
                    LIMIT 1;

                    IF group_display_name IS NULL THEN
                        group_display_name := COALESCE(field_item ->> 'friendly', group_name);
                    END IF;

                    IF (includes_param IS NULL OR group_name = ANY(includes_param))
                    AND (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

                        section_map := section_map || jsonb_build_object(group_name, group_display_name);

                        field_details := field_item -> 'fields';
                        IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                            FOR field_detail IN SELECT jsonb_array_elements(field_details)
                            LOOP
                                benefit_name := field_detail ->> 'friendly';
                                benefit_key := field_detail ->> 'name';
                                carrier_value := field_detail ->> 'value';

                                IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                    carrier_value := '-';
                                     ELSE
                                    -- Validate as numeric if appropriate
                                    BEGIN
                                        PERFORM sandf.safe_parse_numeric(carrier_value);
                                    EXCEPTION WHEN OTHERS THEN
                                        carrier_value := '-';
                                    END;
                                END IF;

                                -- Store coverageLife values for comparison
                                IF benefit_key = 'coverageLife' THEN
                                    coverage_life_values := coverage_life_values || jsonb_build_object(carrier_name, carrier_value);
                                END IF;

                                -- Store values for fields that need to be checked against coverageLife
                                IF benefit_key = ANY(skip_fields) THEN
                                    IF NOT field_values_to_check ? benefit_key THEN
                                        field_values_to_check := field_values_to_check || jsonb_build_object(benefit_key, '{}'::jsonb);
                                    END IF;
                                    field_values_to_check := jsonb_set(
                                        field_values_to_check,
                                        ARRAY[benefit_key, carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END IF;

                                benefit_key_item := group_name || '.' || benefit_key;

                                IF benefit_name IS NULL THEN
                                    SELECT friendly INTO benefit_name
                                    FROM sandf.ui_field
                                    WHERE name = benefit_key
                                    LIMIT 1;
                                    IF benefit_name IS NULL THEN
                                        benefit_name := benefit_key;
                                    END IF;
                                END IF;

                                IF NOT benefit_map ? benefit_key_item THEN
                                    benefit_map := benefit_map || jsonb_build_object(
                                        benefit_key_item,
                                        jsonb_build_object(
                                            'name', benefit_name,
                                            'key', benefit_key,
                                            'section', group_name,
                                            'values', '{}'::jsonb
                                        )
                                    );
                                END IF;

                                benefit_map := jsonb_set(
                                    benefit_map,
                                    ARRAY[benefit_key_item, 'values', carrier_name],
                                    to_jsonb(carrier_value)
                                );
                            END LOOP;
                        END IF;
                    END IF;
                END LOOP;
            ELSE
                -- Fallback to simple key-value object (existing logic)
                DECLARE
                    benefit_data JSONB;
                    field_key TEXT;
                    show_value TEXT;
                BEGIN
                    FOR benefit_key IN SELECT jsonb_object_keys(plan_details)
                    LOOP
                        IF (includes_param IS NULL OR benefit_key = ANY(includes_param))
                        AND (excludes_param IS NULL OR NOT (benefit_key = ANY(excludes_param))) THEN

                            benefit_data := plan_details -> benefit_key;

                            IF benefit_data IS NULL OR jsonb_typeof(benefit_data) != 'object' THEN
                                CONTINUE;
                            END IF;

                            show_value := benefit_data ->> 'show';
                            IF show_value IS NOT NULL AND show_value::boolean = false THEN
                                CONTINUE;
                            END IF;

                            SELECT friendly INTO section_display_name
                            FROM sandf.ui_field
                            WHERE name = benefit_key
                            LIMIT 1;

                            IF section_display_name IS NULL THEN
                                section_display_name := benefit_key;
                            END IF;

                            section_map := section_map || jsonb_build_object(benefit_key, section_display_name);

                            FOR field_key IN SELECT jsonb_object_keys(benefit_data)
                            LOOP
                                IF field_key = 'show' THEN
                                    CONTINUE;
                                END IF;

                                carrier_value := benefit_data ->> field_key;
                                IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
                                    carrier_value := '-';
                                     ELSE
                                    -- Validate as numeric if appropriate
                                    BEGIN
                                        PERFORM sandf.safe_parse_numeric(carrier_value);
                                    EXCEPTION WHEN OTHERS THEN
                                        carrier_value := '-';
                                    END;
                                END IF;

                                -- Store coverageLife values for comparison (fallback section)
                                IF field_key = 'coverageLife' THEN
                                    coverage_life_values := coverage_life_values || jsonb_build_object(carrier_name, carrier_value);
                                END IF;

                                -- Store values for fields that need to be checked against coverageLife (fallback section)
                                IF field_key = ANY(skip_fields) THEN
                                    IF NOT field_values_to_check ? field_key THEN
                                        field_values_to_check := field_values_to_check || jsonb_build_object(field_key, '{}'::jsonb);
                                    END IF;
                                    field_values_to_check := jsonb_set(
                                        field_values_to_check,
                                        ARRAY[field_key, carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END IF;

                                benefit_key_item := benefit_key || '.' || field_key;

                                SELECT friendly INTO benefit_name
                                FROM sandf.ui_field
                                WHERE name = field_key
                                LIMIT 1;

                                IF benefit_name IS NULL THEN
                                    benefit_name := field_key;
                                END IF;

                                IF NOT benefit_map ? benefit_key_item THEN
                                    benefit_map := benefit_map || jsonb_build_object(
                                        benefit_key_item,
                                        jsonb_build_object(
                                            'name', benefit_name,
                                            'key', field_key,
                                            'section', benefit_key,
                                            'values', '{}'::jsonb
                                        )
                                    );
                                END IF;

                                benefit_map := jsonb_set(
                                    benefit_map,
                                    ARRAY[benefit_key_item, 'values', carrier_name],
                                    to_jsonb(carrier_value)
                                );
                            END LOOP;
                        END IF;
                    END LOOP;
                END;
            END IF;
        END IF;
    END LOOP;

    -- Order carriers based on user preferences using the carrier_order_map
    RAISE NOTICE 'Final carrier_order_map: %', carrier_order_map;

    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        RAISE NOTICE 'Adding carrier to ordered array: % with order: %',
                     carrier_item, (carrier_order_map -> carrier_item ->> 'order');
        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(carrier_item);
    END LOOP;

    RAISE NOTICE 'Final ordered carriers: %', ordered_carriers_array;

    -- First, build all sections normally (with skip logic applied)
    FOR section_order_record IN
        SELECT
            key as section_name,
            COALESCE(uf.display_order, 999999) as sort_order
        FROM jsonb_each_text(section_map)
        LEFT JOIN sandf.ui_field uf ON uf.name = key
        ORDER BY sort_order ASC, key ASC
    LOOP
        section_name := section_order_record.section_name;
        section_id := lower(replace(section_name, ' ', ''));
        section_display_name := COALESCE(section_map ->> section_name, section_name);
        benefits_array := '[]'::jsonb;

        -- Order benefits within each section by display_order
        FOR benefit_order_record IN
            SELECT
                key as benefit_key_item,
                value as benefit_obj,
                COALESCE(uf.display_order, 999999) as sort_order
            FROM jsonb_each(benefit_map)
            LEFT JOIN sandf.ui_field uf ON uf.name = (value ->> 'key')
            WHERE (value ->> 'section') = section_name
            ORDER BY sort_order ASC, (value ->> 'key') ASC
        LOOP
            benefit_obj := benefit_order_record.benefit_obj;

            -- Apply skip condition: Check if this field should be skipped
            should_skip_field := FALSE;

            -- Check if this is one of the fields to be compared with coverageLife
            IF (benefit_obj ->> 'key') = ANY(skip_fields) THEN
                should_skip_field := TRUE;

                -- Check if all carriers have the same value for this field AND coverageLife
                FOR carrier_check IN SELECT jsonb_object_keys(coverage_life_values)
                LOOP
                    coverage_value := coverage_life_values ->> carrier_check;
                    field_value := (field_values_to_check -> (benefit_obj ->> 'key')) ->> carrier_check;

                    -- If any carrier has different values, don't skip
                    IF coverage_value != field_value THEN
                        should_skip_field := FALSE;
                        EXIT;
                    END IF;
                END LOOP;
            END IF;

            -- Only add benefit if it shouldn't be skipped
            IF NOT should_skip_field THEN
                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;
        END LOOP;

        section_obj := jsonb_build_object(
            'name', section_display_name,
            'id', section_id,
            'benefits', benefits_array
        );

        all_sections := all_sections || jsonb_build_array(section_obj);
    END LOOP;

    -- Count total benefits across all sections
    total_benefits := 0;
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    -- If total benefits <= MAX_BENEFITS_PER_PAGE, return single object wrapped in array
    IF total_benefits <= MAX_BENEFITS_PER_PAGE THEN
        RETURN jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', all_sections
            )
        )::text;
    END IF;

    -- Otherwise, paginate by redistributing benefits across pages
    current_page_benefits := 0;
    current_page_sections := '[]'::jsonb;
    current_section_benefits := '[]'::jsonb;
    current_section_name := '';

    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        section_obj := all_sections -> section_idx;
        section_name := section_obj ->> 'name';
        section_id := section_obj ->> 'id';
        benefits_array := section_obj -> 'benefits';

        FOR benefit_idx IN 0..jsonb_array_length(benefits_array)-1 LOOP
            benefit_obj := benefits_array -> benefit_idx;

            -- If we're starting a new section or continuing the same section
            IF current_section_name != section_name THEN
                -- Finish previous section if exists
                IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                -- Start new section
                current_section_name := section_name;
                current_section_id := section_id;
                current_section_display_name := section_name;
                current_section_benefits := '[]'::jsonb;
            END IF;

            -- Check if adding this benefit would exceed page limit
            IF current_page_benefits >= MAX_BENEFITS_PER_PAGE THEN
                -- Finish current section
                IF jsonb_array_length(current_section_benefits) > 0 THEN
                    current_page_sections := current_page_sections || jsonb_build_array(
                        jsonb_build_object(
                            'name', current_section_display_name,
                            'id', current_section_id,
                            'benefits', current_section_benefits
                        )
                    );
                END IF;

                -- Create page
                result_pages := result_pages || jsonb_build_array(
                    jsonb_build_object(
                        'carriers', ordered_carriers_array,
                        'sections', current_page_sections
                    )
                );

                -- Reset for new page
                current_page_sections := '[]'::jsonb;
                current_section_benefits := '[]'::jsonb;
                current_page_benefits := 0;
            END IF;

            -- Add benefit to current section
            current_section_benefits := current_section_benefits || jsonb_build_array(benefit_obj);
            current_page_benefits := current_page_benefits + 1;
        END LOOP;
    END LOOP;

    -- Finish last section
    IF current_section_name != '' AND jsonb_array_length(current_section_benefits) > 0 THEN
        current_page_sections := current_page_sections || jsonb_build_array(
            jsonb_build_object(
                'name', current_section_display_name,
                'id', current_section_id,
                'benefits', current_section_benefits
            )
        );
    END IF;

    -- Add final page
    IF jsonb_array_length(current_page_sections) > 0 THEN
        result_pages := result_pages || jsonb_build_array(
            jsonb_build_object(
                'carriers', ordered_carriers_array,
                'sections', current_page_sections
            )
        );
    END IF;

    -- Return array of paginated results
    RETURN result_pages::text;
END;
$$;
        ]]>
        </sql>