  <sql splitStatements="false">
            <![CDATA[
              CREATE OR REPLACE FUNCTION sandf.fn_get_plan_overview(
    includes_param TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    benefit_key TEXT;
    benefit_name TEXT;
    result_array JSONB := '[]'::jsonb;
BEGIN
    -- Loop through includes_param array and build result array with benefit names
    IF includes_param IS NOT NULL THEN
        FOREACH benefit_key IN ARRAY includes_param
        LOOP
            SELECT friendly INTO benefit_name
            FROM sandf.ui_field
            WHERE name = benefit_key
            LIMIT 1;

            IF benefit_name IS NULL THEN
                benefit_name := benefit_key;
            END IF;

            result_array := result_array || jsonb_build_array(benefit_name);
        END LOOP;
    END IF;

    RETURN jsonb_build_object('planOverview', result_array);
END;
$$;
        ]]>
        </sql>