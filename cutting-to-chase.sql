    <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION fn_get_cutting_to_the_chase_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    benefit_premiums_key TEXT := 'benefitPremiums';
    rtq_name TEXT := 'RTQ';
    quote_record RECORD;
    benefit_premiums JSONB;
    carriers_array JSONB := '[]'::jsonb;
    sections_array JSONB := '[]'::jsonb;
    section_obj JSONB;
    benefits_array JSONB := '[]'::jsonb;
    benefit_obj JSONB;
    carrier_name TEXT;
    benefit_map JSONB := '{}'::jsonb;

    -- CuttingToTheChase specific variables for calculations
    total_monthly_premium NUMERIC;
    annual_premium NUMERIC;
    first_carrier_annual NUMERIC := NULL;
    difference_amount NUMERIC;
    difference_percentage NUMERIC;
    carrier_order INT := 0;

    -- New rankings array to store rank as strings
    rankings_array JSONB := '[]'::jsonb;

    -- For user preference ordering
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order_val INTEGER;
    quote_id_val BIGINT;
    quote_uuid_val UUID;
BEGIN
    -- First pass: Build carrier order map and collect all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = rtq_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;
        quote_id_val := quote_record.quote_id;
        quote_uuid_val := quote_record.quote_uuid;
        -- Use the new function to get carrier order
        carrier_order_val := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_id_val,
            quote_uuid_val,
            999999
        );
        -- Build carrier order map using carrier name as key
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order_val,
                    'quote_id', quote_id_val,
                    'quote_uuid', quote_uuid_val
                )
            );
        END IF;
    END LOOP;

    -- Second pass: Process quotes to build plan data with proper ordering
    carrier_order := 0;
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = rtq_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        ORDER BY sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            q.quote_id,
            q.quote_uuid,
            999999
        ) ASC, c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;
        carrier_order := carrier_order + 1;
        -- Append current rank as string to rankings array
        rankings_array := rankings_array || to_jsonb(carrier_order::TEXT);
        -- Add carrier to carriers array if not already present
        IF NOT carriers_array ? carrier_name THEN
            carriers_array := carriers_array || jsonb_build_array(carrier_name);
        END IF;
        -- Extract benefit premiums from formatted_quote_details
        benefit_premiums := quote_record.formatted_quote_details -> benefit_premiums_key;
        -- Process benefit premiums if found
        IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN
            -- Extract totalMonthlyPremiums directly from benefitPremiums
             total_monthly_premium := COALESCE(sandf.safe_parse_numeric(benefit_premiums ->> 'totalMonthlyPremiums'), 0);
            -- Calculate annual premium
            annual_premium := total_monthly_premium * 12;
            -- Set first carrier annual for difference calculations
            IF first_carrier_annual IS NULL THEN
                first_carrier_annual := annual_premium;
            END IF;
            -- Calculate differences
            difference_amount := annual_premium - first_carrier_annual;
            IF first_carrier_annual != 0 THEN
                difference_percentage := (difference_amount / first_carrier_annual) * 100;
            ELSE
                difference_percentage := 0;
            END IF;
            -- Add totalMonthlyPremiums benefit (always include for CuttingToTheChase)
            IF NOT benefit_map ? 'calculations.totalMonthlyPremiums' THEN
                benefit_map := benefit_map || jsonb_build_object(
                    'calculations.totalMonthlyPremiums',
                    jsonb_build_object(
                        'name', 'Total Monthly Premiums',
                        'key', 'totalMonthlyPremiums',
                        'section', 'calculations',
                        'values', '{}'::jsonb
                    )
                );
            END IF;
            benefit_map := jsonb_set(
                benefit_map,
                ARRAY['calculations.totalMonthlyPremiums', 'values', carrier_name],
                to_jsonb(sandf.fn_format_currency_with_symbol_java_style(total_monthly_premium))
            );
            -- Add differencePercentage benefit (always include for CuttingToTheChase)
            IF carrier_order > 1 THEN
                IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        'calculations.differencePercentage',
                        jsonb_build_object(
                            'name', 'Percentage Different From #1',
                            'key', 'differencePercentage',
                            'section', 'calculations',
                            'values', '{}'::jsonb
                        )
                    );
                END IF;
                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY['calculations.differencePercentage', 'values', carrier_name],
                    to_jsonb(sandf.fn_format_percentage_java_style(difference_percentage))
                );
            ELSE
                -- For first carrier, set empty value "-" for difference field
                IF NOT benefit_map ? 'calculations.differencePercentage' THEN
                    benefit_map := benefit_map || jsonb_build_object(
                        'calculations.differencePercentage',
                        jsonb_build_object(
                            'name', 'Percentage Different From #1',
                            'key', 'differencePercentage',
                            'section', 'calculations',
                            'values', '{}'::jsonb
                        )
                    );
                END IF;
                benefit_map := jsonb_set(
                    benefit_map,
                    ARRAY['calculations.differencePercentage', 'values', carrier_name],
                    to_jsonb('-'::text)
                );
            END IF;
        END IF;
    END LOOP;

    -- Build benefits array from benefit_map (only totalMonthlyPremiums and differencePercentage)
    IF benefit_map ? 'calculations.totalMonthlyPremiums' THEN
        benefit_obj := benefit_map -> 'calculations.totalMonthlyPremiums';
        benefits_array := benefits_array || jsonb_build_array(benefit_obj);
    END IF;
    IF benefit_map ? 'calculations.differencePercentage' THEN
        benefit_obj := benefit_map -> 'calculations.differencePercentage';
        benefits_array := benefits_array || jsonb_build_array(benefit_obj);
    END IF;
    -- Only create the "Totals" section if we have benefits
    IF jsonb_array_length(benefits_array) > 0 THEN
        section_obj := jsonb_build_object(
            'name', 'Totals',
            'id', 'calculations',
            'benefits', benefits_array
        );
        sections_array := sections_array || jsonb_build_array(section_obj);
    END IF;
    -- Return the results in the new format including rankings
    RETURN jsonb_build_object(
        'carriers', carriers_array,
        'sections', sections_array,
        'rankings', rankings_array
    );
END;
$$;

        ]]>
        </sql>